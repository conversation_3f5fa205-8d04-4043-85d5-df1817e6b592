import React, { createContext, useContext, useEffect, useState } from 'react'
import { getSystemTheme, isBrowser } from '../lib/utils'

interface ThemeContextType {
  isDark: boolean
  systemTheme: 'light' | 'dark'
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

interface ThemeProviderProps {
  children: React.ReactNode
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const [isDark, setIsDark] = useState<boolean>(() => {
    if (!isBrowser) return false
    return getSystemTheme() === 'dark'
  })

  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>(() => {
    if (!isBrowser) return 'light'
    return getSystemTheme()
  })

  useEffect(() => {
    if (!isBrowser) return

    const root = window.document.documentElement

    // Apply initial theme
    const initialTheme = getSystemTheme()
    setIsDark(initialTheme === 'dark')
    setSystemTheme(initialTheme)

    if (initialTheme === 'dark') {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }

    // Listen for system theme changes
    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      const newTheme = e.matches ? 'dark' : 'light'
      setIsDark(newTheme === 'dark')
      setSystemTheme(newTheme)

      if (newTheme === 'dark') {
        root.classList.add('dark')
      } else {
        root.classList.remove('dark')
      }
    }

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    mediaQuery.addEventListener('change', handleSystemThemeChange)

    return () => mediaQuery.removeEventListener('change', handleSystemThemeChange)
  }, [])

  const value = {
    isDark,
    systemTheme,
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  )
}

export function useTheme() {
  const context = useContext(ThemeContext)

  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }

  return context
}
