import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>eader, CardTitle, CardContent, Alert, AlertTitle, AlertDescription } from '../components'
import { ArrowLeft } from 'lucide-react'

/**
 * Comprehensive example showing how to use your Tailwind config and variants
 */
export function TailwindConfigUsage({ onBack }: { onBack?: () => void }) {
  return (
    <div className="min-h-screen bg-background text-foreground p-8 space-y-8">
      <div className="max-w-4xl mx-auto space-y-8">
        
        {/* Header */}
        <div className="space-y-4">
          {onBack && (
            <Button variant="outline" onClick={onBack} className="mb-4">
              <ArrowLeft className="h-4 w-4" />
              Back to Main
            </Button>
          )}
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold tracking-tight">
              Tailwind Config Usage Examples
            </h1>
            <p className="text-lg text-muted-foreground">
              Practical applications of your custom Tailwind configuration
            </p>
          </div>
        </div>

        {/* 1. Custom Color System */}
        <Card>
          <CardHeader>
            <CardTitle>1. Custom Color System</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            
            {/* Primary Colors */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Primary Color Scale</h3>
              <div className="grid grid-cols-6 gap-2">
                <div className="bg-primary-50 h-16 rounded-lg flex items-center justify-center text-xs font-medium">50</div>
                <div className="bg-primary-100 h-16 rounded-lg flex items-center justify-center text-xs font-medium">100</div>
                <div className="bg-primary-200 h-16 rounded-lg flex items-center justify-center text-xs font-medium">200</div>
                <div className="bg-primary-300 h-16 rounded-lg flex items-center justify-center text-xs font-medium">300</div>
                <div className="bg-primary-400 h-16 rounded-lg flex items-center justify-center text-xs font-medium">400</div>
                <div className="bg-primary-500 h-16 rounded-lg flex items-center justify-center text-xs font-medium text-primary-foreground">500</div>
              </div>
            </div>

            {/* Semantic Colors */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Semantic Colors</h3>
              <div className="grid grid-cols-4 gap-4">
                <div className="bg-success p-4 rounded-lg text-success-foreground text-center font-medium">
                  Success
                </div>
                <div className="bg-warning p-4 rounded-lg text-warning-foreground text-center font-medium">
                  Warning
                </div>
                <div className="bg-destructive p-4 rounded-lg text-destructive-foreground text-center font-medium">
                  Destructive
                </div>
                <div className="bg-info p-4 rounded-lg text-info-foreground text-center font-medium">
                  Info
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 2. Button Variants */}
        <Card>
          <CardHeader>
            <CardTitle>2. Button Variants with CVA</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-3">
              <Button variant="primary">Primary</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="link">Link</Button>
              <Button variant="destructive">Destructive</Button>
            </div>
            
            <div className="flex flex-wrap gap-3">
              <Button size="xs">Extra Small</Button>
              <Button size="sm">Small</Button>
              <Button size="md">Medium</Button>
              <Button size="lg">Large</Button>
              <Button size="xl">Extra Large</Button>
            </div>
          </CardContent>
        </Card>

        {/* 3. Custom Spacing */}
        <Card>
          <CardHeader>
            <CardTitle>3. Custom Spacing System</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="bg-primary-100 h-18 rounded-lg flex items-center justify-center">
                h-18 (4.5rem) - Custom spacing
              </div>
              <div className="bg-secondary-100 h-88 rounded-lg flex items-center justify-center">
                h-88 (22rem) - Custom spacing
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 4. Custom Typography */}
        <Card>
          <CardHeader>
            <CardTitle>4. Custom Typography</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <p className="font-sans text-base">Inter font family (default)</p>
              <p className="font-mono text-sm">JetBrains Mono (monospace)</p>
              <p className="text-xs">Extra small text</p>
              <p className="text-sm">Small text</p>
              <p className="text-base">Base text</p>
              <p className="text-lg">Large text</p>
              <p className="text-xl">Extra large text</p>
            </div>
          </CardContent>
        </Card>

        {/* 5. Custom Animations */}
        <Card>
          <CardHeader>
            <CardTitle>5. Custom Animations</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-primary-100 p-4 rounded-lg animate-fade-in">
                Fade In
              </div>
              <div className="bg-secondary-100 p-4 rounded-lg animate-slide-in">
                Slide In
              </div>
              <div className="bg-accent/20 p-4 rounded-lg animate-scale-in">
                Scale In
              </div>
              <div className="bg-muted p-4 rounded-lg animate-spin-slow">
                Slow Spin
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 6. Alert Variants */}
        <Card>
          <CardHeader>
            <CardTitle>6. Alert Component Variants</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert variant="default">
              <AlertTitle>Default Alert</AlertTitle>
              <AlertDescription>This uses the default alert styling.</AlertDescription>
            </Alert>
            
            <Alert variant="success">
              <AlertTitle>Success Alert</AlertTitle>
              <AlertDescription>Operation completed successfully!</AlertDescription>
            </Alert>
            
            <Alert variant="warning">
              <AlertTitle>Warning Alert</AlertTitle>
              <AlertDescription>Please review before proceeding.</AlertDescription>
            </Alert>
            
            <Alert variant="destructive">
              <AlertTitle>Error Alert</AlertTitle>
              <AlertDescription>Something went wrong.</AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* 7. Badge Variants */}
        <Card>
          <CardHeader>
            <CardTitle>7. Badge Component Variants</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              <Badge variant="default">Default</Badge>
              <Badge variant="secondary">Secondary</Badge>
              <Badge variant="outline">Outline</Badge>
              <Badge variant="destructive">Destructive</Badge>
              <Badge variant="success">Success</Badge>
              <Badge variant="warning">Warning</Badge>
              <Badge variant="info">Info</Badge>
            </div>
          </CardContent>
        </Card>

        {/* 8. Dark Mode Support */}
        <Card>
          <CardHeader>
            <CardTitle>8. Dark Mode Support</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              All colors automatically adapt to dark mode using CSS variables.
              The system detects your preference automatically.
            </p>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-surface border border-border p-4 rounded-lg">
                <h4 className="font-medium text-foreground">Surface Color</h4>
                <p className="text-sm text-muted-foreground">Adapts to theme</p>
              </div>
              <div className="bg-muted p-4 rounded-lg">
                <h4 className="font-medium text-foreground">Muted Background</h4>
                <p className="text-sm text-muted-foreground">Theme-aware</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Code Examples */}
        <Card>
          <CardHeader>
            <CardTitle>9. Code Examples</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-muted p-4 rounded-lg font-mono text-sm">
              <div className="text-muted-foreground mb-2">// Using custom colors</div>
              <div>&lt;div className="bg-primary-500 text-primary-foreground"&gt;</div>
              <div className="ml-4">Primary button</div>
              <div>&lt;/div&gt;</div>
            </div>
            
            <div className="bg-muted p-4 rounded-lg font-mono text-sm">
              <div className="text-muted-foreground mb-2">// Using variants</div>
              <div>&lt;Button variant="primary" size="lg"&gt;</div>
              <div className="ml-4">Click me</div>
              <div>&lt;/Button&gt;</div>
            </div>
          </CardContent>
        </Card>

      </div>
    </div>
  )
}
