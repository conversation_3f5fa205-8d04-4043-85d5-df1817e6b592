import React, { useState } from 'react'
import {
  Button,
  Input,
  Textarea,
  Checkbox,
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
  Alert,
  AlertTitle,
  AlertDescription,
  Badge,
  Avatar,
  AvatarImage,
  AvatarFallback,
  Spinner,
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from './components'
import { useTheme } from './hooks/use-theme'
import { Heart, Star, User, Settings } from 'lucide-react'

function App() {
  const [count, setCount] = useState(0)
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: '',
    newsletter: false
  })
  const { systemTheme } = useTheme()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    setLoading(false)
    alert('Form submitted successfully!')
  }

  return (
    <div className="min-h-screen bg-background text-foreground">
      <div>
  <div className="bg-blue-50 w-10 h-10"></div>
  <div className="bg-blue-100 w-10 h-10"></div>
  <div className="bg-blue-200 w-10 h-10"></div>
  <div className="bg-blue-300 w-10 h-10"></div>
  <div className="bg-blue-400 w-10 h-10"></div>
  <div className="bg-blue-500 w-10 h-10"></div>
  <div className="bg-blue-600 w-10 h-10"></div>
  <div className="bg-blue-700 w-10 h-10"></div>
  <div className="bg-blue-800 w-10 h-10"></div>
  <div className="bg-blue-900 w-10 h-10"></div>
  <div className="bg-blue-950 w-10 h-10"></div>
</div>
      <div>
  <div className="bg-primary w-10 h-10"></div>
  <div className="bg-primary-50 w-10 h-10"></div>
  <div className="bg-primary-100 w-10 h-10"></div>
  <div className="bg-primary-200 w-10 h-10"></div>
  <div className="bg-primary-300 w-10 h-10"></div>
  <div className="bg-primary-400 w-10 h-10"></div>
  <div className="bg-primary-500 w-10 h-10"></div>
  <div className="bg-primary-600 w-10 h-10"></div>
  <div className="bg-primary-700 w-10 h-10"></div>
  <div className="bg-primary-800 w-10 h-10"></div>
  <div className="bg-primary-900 w-10 h-10"></div>
  <div className="bg-primary-950 w-10 h-10"></div>
</div>
      {/* Header */}
      <header className="border-b border-border bg-surface/50 backdrop-blur-sm sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
              <Heart className="h-4 w-4 text-primary-foreground" />
            </div>
            <h1 className="text-xl font-bold">Design System</h1>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="secondary">v1.0.0</Badge>
            <Badge variant="outline" className="capitalize">
              {systemTheme} mode
            </Badge>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8 space-y-8">
        {/* Hero Section */}
        <section className="text-center space-y-4">
          <h2 className="text-4xl font-bold tracking-tight">
            Modern Design System
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Built with Tailwind CSS v4, React, TypeScript, and Vite.
            Featuring automatic {systemTheme} mode detection and accessible components.
          </p>
          <div className="flex items-center justify-center gap-4">
            <Button onClick={() => setCount(count + 1)}>
              Count: {count}
            </Button>
            <Button variant="outline">
              <Star className="h-4 w-4" />
              Star on GitHub
            </Button>
          </div>
        </section>

        {/* Alerts Section */}
        <section className="space-y-4">
          <h3 className="text-2xl font-semibold">Alerts</h3>
          <div className="grid gap-4">
            <Alert>
              <AlertTitle>Info</AlertTitle>
              <AlertDescription>
                This is an informational alert with default styling.
              </AlertDescription>
            </Alert>
            <Alert variant="success">
              <AlertTitle>Success</AlertTitle>
              <AlertDescription>
                Your changes have been saved successfully.
              </AlertDescription>
            </Alert>
            <Alert variant="warning">
              <AlertTitle>Warning</AlertTitle>
              <AlertDescription>
                Please review your settings before proceeding.
              </AlertDescription>
            </Alert>
            <Alert variant="destructive">
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>
                Something went wrong. Please try again.
              </AlertDescription>
            </Alert>
          </div>
        </section>

        {/* Buttons Section */}
        <section className="space-y-4">
          <h3 className="text-2xl font-semibold">Buttons</h3>
          <div className="flex flex-wrap gap-4">
            <Button variant="primary">Primary</Button>
            <Button variant="secondary">Secondary</Button>
            <Button variant="outline">Outline</Button>
            <Button variant="ghost">Ghost</Button>
            <Button variant="link">Link</Button>
            <Button variant="destructive">Destructive</Button>
          </div>
          <div className="flex flex-wrap gap-4">
            <Button size="xs">Extra Small</Button>
            <Button size="sm">Small</Button>
            <Button size="md">Medium</Button>
            <Button size="lg">Large</Button>
            <Button size="xl">Extra Large</Button>
          </div>
          <div className="flex flex-wrap gap-4">
            <Button loading loadingText="Saving...">
              Loading Button
            </Button>
            <Button disabled>Disabled</Button>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon">
                  <Settings className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Settings</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </section>

        {/* Form Section */}
        <section className="space-y-4">
          <h3 className="text-2xl font-semibold">Form Components</h3>
          <Card>
            <CardHeader>
              <CardTitle>Contact Form</CardTitle>
              <CardDescription>
                Fill out this form to get in touch with us.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <Input
                  label="Full Name"
                  placeholder="Enter your full name"
                  required
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                />
                <Input
                  label="Email Address"
                  type="email"
                  placeholder="Enter your email"
                  required
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                />
                <Textarea
                  label="Message"
                  placeholder="Enter your message"
                  required
                  value={formData.message}
                  onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                />
                <Checkbox
                  label="Subscribe to newsletter"
                  checked={formData.newsletter}
                  onCheckedChange={(checked) =>
                    setFormData(prev => ({ ...prev, newsletter: checked as boolean }))
                  }
                />

              </form>
            </CardContent>
            <CardFooter className="gap-2">
              <Button type="submit" loading={loading} loadingText="Submitting...">
                Submit Form
              </Button>
              <Button variant="outline" type="button">
                Cancel
              </Button>
            </CardFooter>
          </Card>
        </section>

        {/* Components Showcase */}
        <section className="space-y-4">
          <h3 className="text-2xl font-semibold">Component Showcase</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Avatar Card */}
            <Card>
              <CardHeader>
                <CardTitle>Avatar</CardTitle>
                <CardDescription>User profile pictures</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-4">
                  <Avatar size="sm">
                    <AvatarImage src="https://github.com/shadcn.png" alt="@shadcn" />
                    <AvatarFallback>CN</AvatarFallback>
                  </Avatar>
                  <Avatar>
                    <AvatarImage src="https://github.com/vercel.png" alt="@vercel" />
                    <AvatarFallback>VC</AvatarFallback>
                  </Avatar>
                  <Avatar size="lg">
                    <AvatarFallback>
                      <User className="h-6 w-6" />
                    </AvatarFallback>
                  </Avatar>
                </div>
              </CardContent>
            </Card>

            {/* Badges Card */}
            <Card>
              <CardHeader>
                <CardTitle>Badges</CardTitle>
                <CardDescription>Status indicators</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  <Badge>Default</Badge>
                  <Badge variant="secondary">Secondary</Badge>
                  <Badge variant="outline">Outline</Badge>
                  <Badge variant="destructive">Error</Badge>
                  <Badge variant="success">Success</Badge>
                  <Badge variant="warning">Warning</Badge>
                  <Badge variant="info">Info</Badge>
                </div>
              </CardContent>
            </Card>

            {/* Loading States */}
            <Card>
              <CardHeader>
                <CardTitle>Loading States</CardTitle>
                <CardDescription>Spinner components</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Spinner size="sm" text="Loading..." />
                  <Spinner size="md" />
                  <Spinner size="lg" text="Please wait" />
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Dialog Example */}
        <section className="space-y-4">
          <h3 className="text-2xl font-semibold">Dialog</h3>
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline">Open Dialog</Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Are you absolutely sure?</DialogTitle>
                <DialogDescription>
                  This action cannot be undone. This will permanently delete your
                  account and remove your data from our servers.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button variant="outline">Cancel</Button>
                <Button variant="destructive">Delete Account</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </section>
      </main>

      {/* Footer */}
      <footer className="border-t border-border bg-surface/50 mt-16">
        <div className="container mx-auto px-4 py-8 text-center text-muted-foreground">
          <p>
            Built with ❤️ using Tailwind CSS v4, React, TypeScript, and Vite
          </p>
          <div className="flex items-center justify-center gap-4 mt-4">
            <Badge variant="outline">Tailwind CSS v4</Badge>
            <Badge variant="outline">React 19</Badge>
            <Badge variant="outline">TypeScript</Badge>
            <Badge variant="outline">Vite</Badge>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default App
