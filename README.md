# Modern Design System

A comprehensive, reusable design system built with the latest web technologies, featuring Tailwind CSS v4, React 19, TypeScript, and Vite with automatic system theme detection.

## ✨ Features

- **🎨 Modern Design**: Built with Tailwind CSS v4 using native CSS variables and the new color system
- **🌙 Automatic Dark Mode**: Uses `prefers-color-scheme` to detect and adapt to system theme preference
- **♿ Accessible**: All components follow accessibility best practices
- **📱 Responsive**: Mobile-first design with responsive breakpoints
- **🎯 Type Safe**: Full TypeScript support with comprehensive type definitions
- **⚡ Fast**: Powered by Vite for lightning-fast development and builds
- **🧩 Modular**: Clean component architecture ready for publishing as a library
- **✨ Smooth Transitions**: All components transition smoothly between light and dark themes

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## 🛠️ Tech Stack

- **React 19** - Latest stable version with modern features
- **TypeScript** - Type-safe development
- **Tailwind CSS v4** - Utility-first CSS with native CSS variables
- **Vite** - Next-generation frontend tooling
- **Radix UI** - Unstyled, accessible UI primitives
- **Lucide React** - Beautiful & consistent icon library
- **Class Variance Authority** - Component variant management

## 🌙 Automatic Dark Mode

The design system automatically detects and adapts to your system's theme preference using `prefers-color-scheme`:

```tsx
import { ThemeProvider, useTheme } from './hooks/use-theme'

function App() {
  return (
    <ThemeProvider>
      <YourApp />
    </ThemeProvider>
  )
}

function YourComponent() {
  const { isDark, systemTheme } = useTheme()

  return (
    <div>
      <p>System theme: {systemTheme}</p>
      <p>Dark mode active: {isDark ? 'Yes' : 'No'}</p>
      {/* Components automatically adapt to system theme */}
    </div>
  )
}
```

### Features:
- **Automatic Detection**: Uses `prefers-color-scheme` media query
- **Real-time Updates**: Instantly responds to system theme changes
- **Smooth Transitions**: All components transition smoothly between themes (200ms ease-in-out)
- **No Manual Toggle**: Respects user's system preference without override options

## 🎨 Design Tokens

The design system uses a comprehensive set of design tokens defined as CSS variables:

### Colors
- **Primary**: Main brand color with opacity variants
- **Secondary**: Supporting brand color
- **Accent**: Highlight color for interactive elements
- **Background/Foreground**: Base colors for content
- **Surface**: Card and elevated surface colors
- **Border**: Consistent border colors
- **Muted**: Subtle text and background colors
- **Semantic Colors**: Success, Warning, Error, Info variants

### Typography
- **Font Family**: Inter (primary), JetBrains Mono (monospace)
- **Font Sizes**: xs, sm, base, lg, xl, 2xl, 3xl, 4xl, 5xl, 6xl
- **Line Heights**: Optimized for readability

### Spacing & Layout
- **Spacing Scale**: Consistent spacing using Tailwind's scale
- **Border Radius**: xs, sm, md, lg, xl, 2xl, 3xl
- **Shadows**: Elevation system with multiple shadow levels
- **Z-Index**: Layering system for overlays and modals

## 🧩 Components

### Form Components
- **Button** - Multiple variants (primary, secondary, outline, ghost, link, destructive)
- **Input** - Text input with validation states
- **Textarea** - Multi-line text input
- **Checkbox** - Accessible checkbox with custom styling
- **Radio Group** - Radio button groups with descriptions
- **Select** - Dropdown select with search and filtering

### Layout Components
- **Card** - Flexible container with header, content, and footer slots
- **Dialog/Modal** - Accessible modal dialogs
- **Alert** - Status messages with different severity levels

### Utility Components
- **Avatar** - User profile pictures with fallbacks
- **Badge** - Status indicators and labels
- **Spinner** - Loading states with different sizes
- **Tooltip** - Contextual help and information

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   └── ui/             # Base UI components
├── hooks/              # Custom React hooks
│   └── use-theme.tsx   # System theme detection
├── lib/                # Utility functions
│   ├── utils.ts        # General utilities
│   └── variants.ts     # Component variants
├── types/              # TypeScript type definitions
│   └── index.ts
└── styles/             # Global styles and tokens
    └── index.css
```
